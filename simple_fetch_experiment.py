#!/usr/bin/env python3
"""
Simple example of fetching an existing experiment using Braintrust SDK.

Usage:
    python simple_fetch_experiment.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update PROJECT_NAME and EXPERIMENT_NAME below
"""

import braintrust

# Configuration - update these with your actual project and experiment names
PROJECT_NAME = "pedro-project1"
EXPERIMENT_NAME = "simple-test"

def main():
    # Fetch existing experiment (read-only)
    experiment = braintrust.init(
        project=PROJECT_NAME,
        experiment=EXPERIMENT_NAME,
        open=True  # This makes it read-only and fetches existing experiment
    )
    
    # Print basic experiment info
    print(f"Experiment: {experiment.name}")
    print(f"Experiment ID: {experiment.id}")
    
    # Iterate through experiment records
    print("\nExperiment records:")
    for i, record in enumerate(experiment.fetch()):
        print(f"Record {i+1}:")
        print(f"  Input: {record.get('input')}")
        print(f"  Output: {record.get('output')}")
        print(f"  Expected: {record.get('expected')}")
        print(f"  Scores: {record.get('scores')}")
        print()
        
        # Stop after first 3 records for demo
        if i >= 2:
            break
    
    print(f"Experiment fetched successfully!")

if __name__ == "__main__":
    main()
